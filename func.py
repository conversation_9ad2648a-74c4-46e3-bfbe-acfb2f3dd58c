from util_img_process import *
import re
from util_ai import ChatBot
from mineru_pdf import trans_pdf_to_markdown
import os
import sys
import importlib.util

# 导入fn_to_markdown_v2函数从根目录的utils.py文件
utils_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'utils.py')
spec = importlib.util.spec_from_file_location("utils_module", utils_path)
utils_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(utils_module)
fn_to_markdown_v2 = utils_module.fn_to_markdown_v2
rag_search_neighbor = utils_module.rag_search_neighbor
markdown_json_to_dict = utils_module.markdown_json_to_dict
import json
import pandas as pd
from datetime import datetime
import docx
from openpyxl import load_workbook
import xlrd
from PIL import Image
import email
import uuid

def markdown_to_json(markdown_text):
    """
    将markdown文本转换为JSON格式，自动去除markdown代码块标记，兼容不同换行符，增加异常处理。
    """
    import json
    # 去除首尾空白
    text = markdown_text.strip()

    # 去除<think>标签和内容
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)

    if '```json' in text:
        # 使用re匹配```json和```之间的内容
        text = re.search(r'```json(.*?)```', text, re.DOTALL).group(1)
        text = text.strip()
    elif '```' in text:
        text = re.search(r'```(.*?)```', text, re.DOTALL).group(1)
        text = text.strip()
    else:
        text = text.strip()
    try:
        json_data = json.loads(text)
        return json_data
    except Exception as e:
        raise ValueError(f"JSON解析失败: {e}\n原始内容: {text}")


# ==================== 非标交易确认单解析核心模块 ====================

def process_file_to_markdown(file_path):
    """
    将不同格式的文件转换为markdown格式
    支持：PDF、Excel、图片、Word文档、邮件文件
    """
    file_ext = os.path.splitext(file_path)[1].lower()

    try:
        if file_ext == '.pdf':
            # 处理PDF文件
            result = trans_pdf_to_markdown(
                file_path,
                parse_method='auto',
                backend='vlm-sglang-engine'
            )
            markdown_content = ""
            for fn_name, data in result['results'].items():
                markdown_content += data['md_content']
            return markdown_content

        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            # 处理图片文件 - 使用OCR方式
            result = trans_pdf_to_markdown(
                file_path,
                parse_method='ocr',
                backend='vlm-sglang-engine'
            )
            markdown_content = ""
            for fn_name, data in result['results'].items():
                markdown_content += data['md_content']
            return markdown_content

        elif file_ext in ['.xlsx', '.xls']:
            # 处理Excel文件（支持新旧格式）
            return process_excel_file(file_path)

        elif file_ext == '.docx':
            # 处理Word文档
            return process_docx_file(file_path)

        elif file_ext == '.eml':
            # 处理邮件文件
            return process_eml_file(file_path)

        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

    except Exception as e:
        print(f"文件处理错误: {e}")
        return None


def process_excel_file(file_path):
    """
    处理Excel文件，转换为markdown格式，保持数字精度
    """
    try:
        file_ext = os.path.splitext(file_path)[1].lower()
        markdown_content = ""

        if file_ext == '.xlsx':
            # 处理新版Excel文件
            try:
                workbook = load_workbook(file_path, data_only=True)
                for sheet_name in workbook.sheetnames:
                    worksheet = workbook[sheet_name]
                    markdown_content += f"\n## {sheet_name}\n\n"

                    # 转换为DataFrame然后转为markdown表格
                    data = []
                    for row in worksheet.iter_rows(values_only=True):
                        row_data = []
                        for cell_value in row:
                            if cell_value is None:
                                cell_value = ""
                            elif isinstance(cell_value, (int, float)):
                                # 保持数字精度，使用高精度字符串转换
                                if isinstance(cell_value, float) and cell_value.is_integer():
                                    cell_value = int(cell_value)
                                else:
                                    # 保持原始浮点数精度
                                    cell_value = f"{cell_value:.10g}"
                            row_data.append(str(cell_value))

                        if any(cell != "" for cell in row_data):
                            data.append(row_data)

                    if data:
                        df = pd.DataFrame(data[1:], columns=data[0] if data else [])
                        markdown_content += df.to_markdown(index=False, floatfmt='.10g') + "\n\n"

            except Exception as e:
                print(f"处理.xlsx文件时出错: {e}")
                return None

        elif file_ext == '.xls':
            # 处理老版Excel文件
            return process_xls_file(file_path)

        return markdown_content

    except Exception as e:
        print(f"Excel文件处理错误: {e}")
        return None


def process_xls_file(file_path):
    """
    处理.xls文件（老版Excel格式），保持数字精度
    """
    try:
        # 读取老版Excel文件
        workbook = xlrd.open_workbook(file_path)
        markdown_content = ""

        for sheet_index in range(workbook.nsheets):
            worksheet = workbook.sheet_by_index(sheet_index)
            sheet_name = workbook.sheet_names()[sheet_index]
            markdown_content += f"\n## {sheet_name}\n\n"

            # 转换为DataFrame然后转为markdown表格
            data = []
            for row_idx in range(worksheet.nrows):
                row_data = []
                for col_idx in range(worksheet.ncols):
                    cell_value = worksheet.cell_value(row_idx, col_idx)
                    # 处理不同类型的单元格值，保持数字精度
                    if isinstance(cell_value, float):
                        # 保持浮点数的原始精度，使用高精度字符串转换
                        if cell_value.is_integer():
                            # 只有当浮点数确实是整数时才转换为int
                            cell_value = int(cell_value)
                        else:
                            # 保持原始浮点数精度，使用高精度字符串格式化
                            # 使用.10g格式保留10位有效数字，避免四舍五入
                            cell_value = f"{cell_value:.10g}"
                    row_data.append(str(cell_value) if cell_value != '' else "")

                if any(cell != "" for cell in row_data):
                    data.append(row_data)

            if data:
                df = pd.DataFrame(data[1:], columns=data[0] if data else [])
                # 保持数字精度，不使用科学计数法，保留足够的小数位
                markdown_content += df.to_markdown(index=False, floatfmt='.10g') + "\n\n"

        return markdown_content

    except Exception as e:
        print(f".xls文件处理错误: {e}")
        return None


def process_docx_file(file_path):
    """
    处理Word文档，转换为markdown格式
    """
    try:
        doc = docx.Document(file_path)
        markdown_content = ""

        # 处理段落
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                markdown_content += paragraph.text + "\n"

        # 处理表格
        for table in doc.tables:
            markdown_content += "\n"
            for i, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    row_data.append(cell.text.strip())

                if i == 0:
                    # 表头
                    markdown_content += "| " + " | ".join(row_data) + " |\n"
                    markdown_content += "| " + " | ".join(["---"] * len(row_data)) + " |\n"
                else:
                    # 数据行
                    markdown_content += "| " + " | ".join(row_data) + " |\n"
            markdown_content += "\n"

        return markdown_content

    except Exception as e:
        print(f"Word文档处理错误: {e}")
        return None


def process_eml_file(file_path):
    """
    处理.eml文件（邮件格式），转换为markdown格式
    """
    try:
        # 读取邮件文件
        with open(file_path, 'rb') as f:
            msg = email.message_from_bytes(f.read())

        markdown_content = ""

        # 提取邮件头信息
        markdown_content += f"# 邮件信息\n\n"
        markdown_content += f"**发件人**: {msg.get('From', 'N/A')}\n"
        markdown_content += f"**收件人**: {msg.get('To', 'N/A')}\n"
        markdown_content += f"**主题**: {msg.get('Subject', 'N/A')}\n"
        markdown_content += f"**日期**: {msg.get('Date', 'N/A')}\n\n"

        # 提取邮件正文
        markdown_content += f"## 邮件正文\n\n"

        if msg.is_multipart():
            # 多部分邮件
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                # 只处理正文，不处理附件
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        markdown_content += body + "\n\n"
                    except:
                        try:
                            body = part.get_payload(decode=True).decode('gbk', errors='ignore')
                            markdown_content += body + "\n\n"
                        except:
                            markdown_content += "[无法解码的文本内容]\n\n"

                elif content_type == "text/html" and "attachment" not in content_disposition:
                    try:
                        html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        try:
                            html_body = part.get_payload(decode=True).decode('gbk', errors='ignore')
                        except:
                            html_body = "[无法解码的HTML内容]"

                    # 简单的HTML标签清理
                    clean_text = re.sub('<[^<]+?>', '', html_body)
                    # 清理多余的空白字符
                    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
                    markdown_content += clean_text + "\n\n"
        else:
            # 单部分邮件
            try:
                body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                markdown_content += body + "\n\n"
            except:
                try:
                    body = msg.get_payload(decode=True).decode('gbk', errors='ignore')
                    markdown_content += body + "\n\n"
                except:
                    markdown_content += "[无法解码的邮件内容]\n\n"

        return markdown_content

    except Exception as e:
        print(f".eml文件处理错误: {e}")
        return None


def parse_transaction_document(file_path, save_result=True, output_dir="./output"):
    """
    解析交易确认单文档的主函数
    """
    print(f"开始解析文档...")
    print(f"开始处理文件: {file_path}")

    # 步骤1: 转换文件为markdown格式
    print("正在转换文件为markdown格式...")
    markdown_content = process_file_to_markdown(file_path)

    if not markdown_content:
        return {
            'success': False,
            'error': '文件转换失败',
            'file_path': file_path
        }

    print(f"文件转换完成，内容长度: {len(markdown_content)} 字符")

    # 步骤2: 使用大模型解析交易信息
    print("正在使用大模型解析交易信息...")
    try:
        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('non_standard_trade', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            system_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的非标交易解析提示词
            system_prompt = """你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。

=====================
【必须提取的字段】
1. 投资者名称：通常指代客户姓名，一般是资管计划的名称
2. 投资者账号：通常指代客户的资金账号
3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填"/"）
4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回
5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填"/"）
7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填"/"）
8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填"/"）
9. 交易费用：一般申购、赎回、买入、卖出交易中，会标明交易费用（数字格式，保持原始精度不要四舍五入，缺失填"/"）

=====================
【重要注意事项】
- 所有数字字段（投资标的金额、投资标的数量、交易费用）必须保持原始精度，不要进行四舍五入
- 如果原始数据是31006.5，输出应该是"31006.5"，不要变成"31007"或"31006"
- 如果原始数据是1386040，输出应该是"1386040"，不要变成"1.38604e+06"

=====================
【业务类型映射】
- 分红：分红派息、现金分红、股息分红等
- 红利转投：红利再投资、分红转投等
- 买入：买入、购买等
- 卖出：卖出、赎回卖出等
- 认购：认购、新基金认购等
- 申购：申购、基金申购等
- 赎回：赎回、基金赎回等

=====================
【输出JSON格式】
如果文档包含多笔交易，请返回数组格式。确保数字字段保持原始精度，不要四舍五入。单笔交易示例：
{
  "投资者名称": "某某资管计划",
  "投资者账号": "********9",
  "业务日期": "2024-01-01",
  "业务类型": "申购",
  "投资标的名称": "某某基金",
  "投资标的代码": "ABC123",
  "投资标的金额": "1000000.00",
  "投资标的数量": "100000.00",
  "交易费用": "1000.00"
}
        """
        
        # 初始化聊天机器人（已优化数字精度处理）
        chatbot = ChatBot(
            model='qwen3-32b',
            system_prompt=system_prompt
        )

        response = chatbot.chat(markdown_content)
        print("大模型解析完成")

        # 步骤3: 解析JSON结果
        json_str = response.strip()
        if json_str.startswith('```json'):
            json_str = json_str[7:]
        if json_str.endswith('```'):
            json_str = json_str[:-3]
        json_str = json_str.strip()

        parsed_result = json.loads(json_str)

        # 步骤4: 保存结果
        if save_result:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.splitext(os.path.basename(file_path))[0]

            # 保存JSON结果
            result_file = os.path.join(output_dir, f"{filename}_{timestamp}_result.json")
            os.makedirs(output_dir, exist_ok=True)

            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_result, f, ensure_ascii=False, indent=2)

            # 保存Markdown内容
            markdown_file = os.path.join(output_dir, f"{filename}_{timestamp}_markdown.md")
            with open(markdown_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            print(f"结果已保存到: {result_file}")
            print(f"Markdown内容已保存到: {markdown_file}")

        return {
            'success': True,
            'parsed_result': parsed_result,
            'markdown_content': markdown_content,
            'file_path': file_path
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'file_path': file_path,
            'markdown_content': markdown_content
        }


def non_standard_trade_analysis(file_path):
    """
    非标交易确认单解析 - 统一入口函数
    """
    try:
        # 默认返回格式
        default_result = {
            "交易信息": {
                "交易类型": "/",
                "交易金额": "/",
                "交易日期": "/",
                "交易对手": "/"
            },
            "产品信息": {
                "产品名称": "/",
                "产品代码": "/"
            },
            "确认信息": {
                "确认状态": "/",
                "确认日期": "/",
                "确认机构": "/"
            }
        }
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return default_result
        
        # 调用实际的解析函数
        result = parse_transaction_document(file_path, save_result=False)
        
        if result and result.get('success'):
            parsed_result = result.get('parsed_result')
            if parsed_result:
                # 如果解析成功，返回解析结果
                if isinstance(parsed_result, dict):
                    return {**default_result, **parsed_result}
                elif isinstance(parsed_result, list) and len(parsed_result) > 0:
                    # 如果是列表，返回第一个元素
                    return {**default_result, **parsed_result[0]}
                else:
                    return default_result
            else:
                return default_result
        else:
            error_msg = result.get('error', '解析失败') if result else '解析函数返回空结果'
            print(f"非标交易确认单解析失败: {error_msg}")
            return default_result
            
    except Exception as e:
        print(f"非标交易确认单解析失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


# ==================== 批量处理功能 ====================

def batch_process_documents(input_dir, output_dir="./output", supported_extensions=None, recursive=True):
    """
    批量处理文档（支持递归处理子目录）

    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        supported_extensions: 支持的文件扩展名列表
        recursive: 是否递归处理子目录

    Returns:
        dict: 处理结果字典，包含详细信息和汇总数据
    """
    if supported_extensions is None:
        supported_extensions = ['.pdf', '.xlsx', '.xls', '.jpg', '.jpeg', '.png', '.docx', '.bmp', '.gif', '.tiff', '.webp', '.eml']

    results = []
    all_transactions = []  # 汇总所有交易数据

    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在 - {input_dir}")
        return {'success': False, 'error': f'目录不存在: {input_dir}'}

    # 获取所有支持的文件（递归或非递归）
    files_to_process = []
    if recursive:
        # 递归遍历所有子目录
        for root, dirs, files in os.walk(input_dir):
            for filename in files:
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in supported_extensions:
                    files_to_process.append(os.path.join(root, filename))
    else:
        # 只处理当前目录
        for filename in os.listdir(input_dir):
            if os.path.isfile(os.path.join(input_dir, filename)):
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in supported_extensions:
                    files_to_process.append(os.path.join(input_dir, filename))

    print(f"找到 {len(files_to_process)} 个文件需要处理")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 逐个处理文件
    for i, file_path in enumerate(files_to_process, 1):
        print(f"\n处理第 {i}/{len(files_to_process)} 个文件: {os.path.basename(file_path)}")

        try:
            result = parse_transaction_document(file_path, save_result=True, output_dir=output_dir)

            # 添加文件相对路径信息
            if result:
                result['relative_path'] = os.path.relpath(file_path, input_dir)
                result['file_name'] = os.path.basename(file_path)

            results.append(result)

            if result and result.get('success'):
                print(f"✓ 处理成功")

                # 收集交易数据用于汇总
                parsed_data = result.get('parsed_result')
                if parsed_data:
                    if isinstance(parsed_data, list):
                        for transaction in parsed_data:
                            transaction['源文件'] = os.path.basename(file_path)
                            transaction['文件路径'] = result['relative_path']
                            all_transactions.append(transaction)
                    else:
                        parsed_data['源文件'] = os.path.basename(file_path)
                        parsed_data['文件路径'] = result['relative_path']
                        all_transactions.append(parsed_data)
            else:
                print(f"✗ 处理失败: {result.get('error', '未知错误') if result else '返回结果为空'}")

        except Exception as e:
            print(f"✗ 处理异常: {e}")
            results.append({
                'success': False,
                'file_path': file_path,
                'relative_path': os.path.relpath(file_path, input_dir),
                'file_name': os.path.basename(file_path),
                'error': str(e)
            })

    # 生成统计信息
    successful_count = sum(1 for r in results if r and r.get('success'))
    failed_count = len(files_to_process) - successful_count

    print(f"\n批量处理完成: {successful_count}/{len(files_to_process)} 个文件处理成功")
    print(f"成功解析的交易记录总数: {len(all_transactions)}")

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存详细批量处理报告
    report_file = os.path.join(output_dir, f"batch_report_{timestamp}.json")
    batch_report = {
        'timestamp': timestamp,
        'input_dir': input_dir,
        'output_dir': output_dir,
        'total_files': len(files_to_process),
        'successful_files': successful_count,
        'failed_files': failed_count,
        'total_transactions': len(all_transactions),
        'processing_details': results
    }

    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(batch_report, f, ensure_ascii=False, indent=2)

    # 保存交易数据汇总
    summary_file = os.path.join(output_dir, f"transactions_summary_{timestamp}.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(all_transactions, f, ensure_ascii=False, indent=2)

    print(f"批量处理报告已保存到: {report_file}")
    print(f"交易数据汇总已保存到: {summary_file}")

    return {
        'success': True,
        'timestamp': timestamp,
        'report_file': report_file,
        'summary_file': summary_file,
        'batch_report': batch_report,
        'all_transactions': all_transactions,
        'results': results
    }


# ==================== 结果对比功能 ====================

def compare_batch_results(summary_file1, summary_file2, output_dir="./output"):
    """
    对比两次批量处理的结果

    Args:
        summary_file1: 第一次处理的汇总文件路径
        summary_file2: 第二次处理的汇总文件路径
        output_dir: 输出目录

    Returns:
        dict: 对比结果
    """
    try:
        # 读取两个汇总文件
        with open(summary_file1, 'r', encoding='utf-8') as f:
            data1 = json.load(f)

        with open(summary_file2, 'r', encoding='utf-8') as f:
            data2 = json.load(f)

        # 创建文件名到交易数据的映射
        def create_file_mapping(data):
            mapping = {}
            for transaction in data:
                file_name = transaction.get('源文件', 'unknown')
                if file_name not in mapping:
                    mapping[file_name] = []
                mapping[file_name].append(transaction)
            return mapping

        mapping1 = create_file_mapping(data1)
        mapping2 = create_file_mapping(data2)

        # 对比分析
        all_files = set(mapping1.keys()) | set(mapping2.keys())

        comparison_result = {
            'summary': {
                'first_batch': {
                    'file': summary_file1,
                    'total_transactions': len(data1),
                    'total_files': len(mapping1)
                },
                'second_batch': {
                    'file': summary_file2,
                    'total_transactions': len(data2),
                    'total_files': len(mapping2)
                },
                'differences': {
                    'transaction_count_diff': len(data2) - len(data1),
                    'file_count_diff': len(mapping2) - len(mapping1)
                }
            },
            'file_level_comparison': {},
            'new_files': [],
            'removed_files': [],
            'modified_files': []
        }

        # 文件级别对比
        for file_name in all_files:
            transactions1 = mapping1.get(file_name, [])
            transactions2 = mapping2.get(file_name, [])

            if not transactions1 and transactions2:
                # 新增文件
                comparison_result['new_files'].append({
                    'file_name': file_name,
                    'transaction_count': len(transactions2)
                })
            elif transactions1 and not transactions2:
                # 删除文件
                comparison_result['removed_files'].append({
                    'file_name': file_name,
                    'transaction_count': len(transactions1)
                })
            elif transactions1 and transactions2:
                # 对比交易数据
                if len(transactions1) != len(transactions2):
                    comparison_result['modified_files'].append({
                        'file_name': file_name,
                        'first_batch_count': len(transactions1),
                        'second_batch_count': len(transactions2),
                        'difference': len(transactions2) - len(transactions1)
                    })

                # 详细对比（简化版，只对比数量和基本信息）
                comparison_result['file_level_comparison'][file_name] = {
                    'first_batch_transactions': len(transactions1),
                    'second_batch_transactions': len(transactions2),
                    'difference': len(transactions2) - len(transactions1)
                }

        # 保存对比结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_file = os.path.join(output_dir, f"batch_comparison_{timestamp}.json")

        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_result, f, ensure_ascii=False, indent=2)

        print(f"\n=== 批量处理结果对比 ===")
        print(f"第一次处理: {len(data1)} 笔交易，{len(mapping1)} 个文件")
        print(f"第二次处理: {len(data2)} 笔交易，{len(mapping2)} 个文件")
        print(f"交易数量变化: {len(data2) - len(data1):+d}")
        print(f"文件数量变化: {len(mapping2) - len(mapping1):+d}")

        if comparison_result['new_files']:
            print(f"\n新增文件 ({len(comparison_result['new_files'])} 个):")
            for file_info in comparison_result['new_files'][:5]:  # 只显示前5个
                print(f"  + {file_info['file_name']} ({file_info['transaction_count']} 笔交易)")
            if len(comparison_result['new_files']) > 5:
                print(f"  ... 还有 {len(comparison_result['new_files']) - 5} 个新增文件")

        if comparison_result['removed_files']:
            print(f"\n删除文件 ({len(comparison_result['removed_files'])} 个):")
            for file_info in comparison_result['removed_files'][:5]:
                print(f"  - {file_info['file_name']} ({file_info['transaction_count']} 笔交易)")
            if len(comparison_result['removed_files']) > 5:
                print(f"  ... 还有 {len(comparison_result['removed_files']) - 5} 个删除文件")

        if comparison_result['modified_files']:
            print(f"\n修改文件 ({len(comparison_result['modified_files'])} 个):")
            for file_info in comparison_result['modified_files'][:5]:
                print(f"  ~ {file_info['file_name']} ({file_info['first_batch_count']} -> {file_info['second_batch_count']})")
            if len(comparison_result['modified_files']) > 5:
                print(f"  ... 还有 {len(comparison_result['modified_files']) - 5} 个修改文件")

        print(f"\n对比结果已保存到: {comparison_file}")

        return {
            'success': True,
            'comparison_file': comparison_file,
            'comparison_result': comparison_result
        }

    except Exception as e:
        print(f"对比过程中发生错误: {e}")
        return {
            'success': False,
            'error': str(e)
        }


# ==================== Excel导出功能 ====================

def export_summary_to_excel(summary_file, output_dir="./output"):
    """
    将汇总JSON文件导出为Excel
    """
    if not os.path.exists(summary_file):
        print(f"汇总文件不存在: {summary_file}")
        return None

    try:
        # 读取JSON文件
        with open(summary_file, 'r', encoding='utf-8') as f:
            all_transactions = json.load(f)

        if not all_transactions:
            print("汇总文件中没有交易记录")
            return None

        # 生成Excel文件名
        base_name = os.path.splitext(os.path.basename(summary_file))[0]
        excel_file = os.path.join(output_dir, f"{base_name}.xlsx")

        # 创建DataFrame
        df = pd.DataFrame(all_transactions)

        # 重新排列列的顺序
        columns_order = ['源文件', '文件路径', '投资者名称', '投资者账号', '业务日期', '业务类型',
                        '投资标的名称', '投资标的代码', '投资标的金额', '投资标的数量', '交易费用']

        # 只保留存在的列
        existing_columns = [col for col in columns_order if col in df.columns]
        # 添加其他存在但不在预定义列表中的列
        other_columns = [col for col in df.columns if col not in existing_columns]
        final_columns = existing_columns + other_columns

        df = df[final_columns]

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 导出Excel
        df.to_excel(excel_file, index=False)

        print(f"汇总数据已导出到Excel文件: {excel_file}")
        print(f"共导出 {len(all_transactions)} 笔交易记录")
        print(f"包含列: {', '.join(final_columns)}")

        return excel_file

    except Exception as e:
        print(f"导出Excel时发生错误: {e}")
        return None


# ==================== 依赖检查和安装 ====================

def check_and_install_dependencies():
    """
    检查和安装依赖库
    """
    import subprocess
    import sys

    required_packages = ['tabulate', 'openpyxl', 'xlrd', 'python-docx', 'PyPDF2', 'Pillow']

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装完成")


def future_account_analysis_v16(img_path):
    """
    期货账户分析V1.6版本 - 基于模板代码逻辑
    使用fn_to_markdown_v2和更完整的处理逻辑
    """
    try:
        # 标准化返回格式的默认值（基于V1.6版本）
        default_result = {
            "产品名称": "/",
            "资金账号": "/",
            "会员号": {
                "上期所": "/",
                "大商所": "/",
                "郑商所": "/",
                "中金所": "/",
                "上能所": "/",
                "广期所": "/"
            },
            "交易编码": {
                "上期所": {"投机": "/", "套利": "/", "套保": "/"},
                "大商所": {"投机": "/", "套利": "/", "套保": "/"},
                "郑商所": {"投机": "/", "套利": "/", "套保": "/"},
                "中金所": {"投机": "/", "套利": "/", "套保": "/"},
                "上能所": {"投机": "/", "套利": "/", "套保": "/"},
                "广期所": {"投机": "/", "套利": "/", "套保": "/"}
            },
            "开始时间": "/",
            "结束时间": "/"
        }

        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result

        # V1.6版本的系统提示词
        system_prompt = """你是一名期货开户文件解析专家。请严格区分"会员号"（固定 4 位数字）和"交易编码"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在部分时候也被称为`交易编码对应名称`
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填"/"）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为"投机"）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填"/"）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填"/"）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所、上海交易所
- 大商所＝大连商品交易所、大连交易所
- 郑商所＝郑州商品交易所、郑州交易所
- 中金所＝中国金融期货交易所、金融交易所
- 上能所＝上海能源交易所、能源中心、能源所
- 广期所＝广州期货交易所、广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认"投机"。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
```json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
```
"""

        # 使用V1.6版本的处理逻辑
        try:
            if img_path.endswith(".pdf"):
                # PDF文件处理 - 使用fn_to_markdown_v2
                markdown_content, seal_img_list = fn_to_markdown_v2(img_path, convert_to_scanned=False, ai_seal=True, ai_model='InternVL3-38B', add_ocr_info=False)
            else:
                # 图片文件处理 - 转为扫描式PDF
                markdown_content, seal_img_list = fn_to_markdown_v2(img_path, convert_to_scanned=True, ai_seal=True, ai_model='InternVL3-38B')

            # 创建ChatBot实例
            chatbot = ChatBot(model='InternVL3-38B', system_prompt=system_prompt)

            # 根据是否有印章图片选择处理方式
            if len(seal_img_list) > 0:
                response = chatbot.chat_with_img(markdown_content, img_url=seal_img_list)
            else:
                response = chatbot.chat(markdown_content)

            # 解析JSON结果
            result = markdown_to_json(response)

            if isinstance(result, dict) and 'error' not in result:
                # 合并默认值，确保结构完整
                for key in default_result:
                    if key not in result:
                        result[key] = default_result[key]
                return result
            else:
                return default_result

        except Exception as e:
            print(f"期货账户V1.6分析失败: {e}")
            return default_result

    except Exception as e:
        print(f"期货账户V1.6分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def future_account_analysis(img_path):
    """
    期货账户分析（对应期货交易会员解析V1.6）
    """
    try:
        # 标准化返回格式的默认值（基于V1.6版本）
        default_result = {
            "产品名称": "/",
            "资金账号": "/",
            "会员号": {
                "上期所": "/",
                "大商所": "/",
                "郑商所": "/",
                "中金所": "/",
                "上能所": "/",
                "广期所": "/"
            },
            "交易编码": {
                "上期所": {"投机": "/", "套利": "/", "套保": "/"},
                "大商所": {"投机": "/", "套利": "/", "套保": "/"},
                "郑商所": {"投机": "/", "套利": "/", "套保": "/"},
                "中金所": {"投机": "/", "套利": "/", "套保": "/"},
                "上能所": {"投机": "/", "套利": "/", "套保": "/"},
                "广期所": {"投机": "/", "套利": "/", "套保": "/"}
            },
            "开始时间": "/",
            "结束时间": "/"
        }
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('futures_account', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的期货交易会员解析V1.6版本的prompt
            img_prompt = """你是一名期货开户文件解析专家。请严格区分"会员号"（固定 4 位数字）和"交易编码"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在部分时候也被称为`交易编码对应名称`
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填"/"）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为"投机"）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填"/"）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填"/"）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所、上海交易所
- 大商所＝大连商品交易所、大连交易所
- 郑商所＝郑州商品交易所、郑州交易所
- 中金所＝中国金融期货交易所、金融交易所
- 上能所＝上海能源交易所、能源中心、能源所
- 广期所＝广州期货交易所、广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认"投机"。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
```json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
```
"""
        
        # 判断文件类型并处理
        file_ext = img_path.lower()
        is_image = any(file_ext.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'])
        
        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值，确保结构完整
                    for key in default_result:
                        if key not in result:
                            result[key] = default_result[key]
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"期货账户图片分析失败: {e}")
                return default_result
        else:
            # PDF文件处理
            try:
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result
                
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt="""你是一名期货开户文件解析专家。请根据用户提供的期货开户/备案文件内容，提取相关信息并按JSON格式输出。请注意，输出格式必须为json格式，不要输出其他内容。"""
                )
                
                response = chatbot.chat(markdown_content + "\n\n请按照期货账户分析的标准格式输出JSON结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值，确保结构完整
                    for key in default_result:
                        if key not in result:
                            result[key] = default_result[key]
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"期货账户PDF分析失败: {e}")
                return default_result
                
    except Exception as e:
        print(f"期货账户分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def broker_interest_change_analysis(img_path):
    """
    券商账户计息变更分析（对应券商账户计息变更v1.3）
    """
    try:
        # 默认返回格式
        default_result = [
            {
                "产品名称": "/",
                "产品类别": "单产品",
                "利率(年化)": {"all": "/"},
                "开始时间": "/",
                "截止时间": "/",
                "计息天数": "/",
                "备注": "/"
            }
        ]
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('broker_interest', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的券商账户计息变更v1.3.ipynb中的prompt
            img_prompt = """请从图片中提取券商账户计息变更的相关信息，严格按照以下格式输出JSON数组：

=====================【必须提取的字段】=====================
1. **产品名称**：具体的产品或资产名称
2. **产品类别**：区分是"单产品"还是"全公司产品"等
3. **利率(年化)**：
   • 如果是统一利率，格式为 `{"all": "X.XX%"}`
   • 如果按客户类型分段，格式为 `{"个人": "X.XX%", "非个人": "X.XX%"}`
   • 如果按时间分段，格式为 `{"START:YYYY-MM-DD": "X.XX%", "YYYY-MM-DD:END": "X.XX%"}`
4. **开始时间**：变更生效的开始日期（YYYY-MM-DD格式）
5. **截止时间**：变更的截止日期，如无明确截止则填""  
6. **计息天数**：年化计息的天数基础（如360天、365天等）
7. **备注**：其他重要信息

=====================【输出JSON数组示例】=====================
```json
[
  {
    "产品名称": "汇添富远景成长一年持有期混合型基金",
    "产品类别": "单产品",
    "利率(年化)": {"all": "1.4%"},
    "开始时间": "2025-01-02",
    "截止时间": "",
    "计息天数": 360,
    "备注": "按月20日前结息至期货账户"
  }
]
```
"""

        # 判断文件类型并处理
        file_ext = img_path.lower()
        is_image = any(file_ext.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'])
        
        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, list) and len(result) > 0:
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"券商计息变更图片分析失败: {e}")
                return default_result
        else:
            # PDF文件处理
            try:
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result
                
                chatbot_system_prompt = """你是一位资深的银行托管部经理，负责从客户提供的文件中抽取基金计息变更要素，并按JSON数组格式输出。请注意，输出格式必须为json格式，不要输出其他内容。"""
                
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt=chatbot_system_prompt
                )
                
                response = chatbot.chat(markdown_content + "\n\n请按照券商账户计息变更的标准格式输出JSON数组结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, list) and len(result) > 0:
                    return result
                else:
                    return default_result
            except Exception as e:
                print(f"券商计息变更PDF分析失败: {e}")
                return default_result
                
    except Exception as e:
        print(f"券商账户计息变更分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}



def ningyin_fee_change_analysis(img_path):
    """
    宁银理财费用变更分析（对应宁银理财费用变更样例）
    """
    try:
        # 默认返回格式
        default_result = {
            "产品信息": {
                "产品名称": "/",
                "产品代码": "/"
            },
            "费用变更": [
                {
                    "费用类型": "/",
                    "变更前费率": "/",
                    "变更后费率": "/",
                    "变更幅度": "/"
                }
            ],
            "变更信息": {
                "生效日期": "/",
                "公告日期": "/",
                "变更原因": "/",
                "公告编号": "/"
            }
        }
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 判断文件类型
        file_ext = img_path.lower()
        image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
        is_image = any(file_ext.endswith(ext) for ext in image_formats)
        is_pdf = file_ext.endswith('.pdf')

        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('ningxia_bank_fee', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的宁银理财费用变更样例中的prompt
            img_prompt = """请从图片中提取宁银理财费用变更的相关信息，严格按照JSON格式输出：

请以JSON格式输出，格式如下：
```json
{
  "产品信息": {
    "产品名称": "产品名称",
    "产品代码": "产品代码"
  },
  "费用变更": [
    {
      "费用类型": "管理费",
      "变更前费率": "0.50%",
      "变更后费率": "0.30%",
      "变更幅度": "下调0.20%"
    }
  ],
  "变更信息": {
    "生效日期": "2024-01-01",
    "公告日期": "2023-12-15",
    "变更原因": "优惠活动",
    "公告编号": "NBYC2023001"
  }
}
```
"""

        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值确保结构完整
                    return {**default_result, **result}
                else:
                    return default_result
            except Exception as e:
                print(f"宁银费用变更图片分析失败: {e}")
                return default_result
        elif is_pdf:
            # PDF文件处理
            try:
                # 使用process_file_to_markdown进行PDF转换
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result

                # 使用ChatBot进行markdown内容分析
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt="""你是一个宁银理财费用变更分析专家，请根据用户提供的费用变更公告内容，提取相关信息。
                    请注意，输出格式必须为json格式，不要输出其他内容。

                    # 示例输出(仅供参考，请根据实际情况输出)
                    ```json
                    {
                      "产品信息": {
                        "产品名称": "宁银理财XX产品",
                        "产品代码": "NBYC001"
                      },
                      "费用变更": [
                        {
                          "费用类型": "管理费",
                          "变更前费率": "0.50%",
                          "变更后费率": "0.30%",
                          "变更幅度": "下调0.20%"
                        }
                      ],
                      "变更信息": {
                        "生效日期": "2024-01-01",
                        "公告日期": "2023-12-15",
                        "变更原因": "优惠活动",
                        "公告编号": "NBYC2023001"
                      }
                    }
                    ```
                    """
                )
                response = chatbot.chat(markdown_content + "\n\n请按照宁银理财费用变更的标准格式输出JSON结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, dict) and 'error' not in result:
                    # 合并默认值确保结构完整
                    return {**default_result, **result}
                else:
                    return default_result
                    
            except Exception as e:
                print(f"宁银费用变更PDF分析失败: {e}")
                return default_result
        else:
            print(f"不支持的文件格式: {file_ext}")
            return default_result
            
    except Exception as e:
        print(f"宁银理财费用变更分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def broker_interest_analysis_v16(file_path):
    """
    Broker Interest Analysis V1.6 - Based on notebook logic
    """
    try:
        default_result = [{"product_name": "/", "product_type": "/", "interest_rate": {}, "start_time": "/", "end_time": "/", "interest_days": "/", "remarks": "/"}]
        
        if not os.path.exists(file_path):
            return default_result
        
        # Use fn_to_markdown_v2 for document processing
        if file_path.endswith(".pdf"):
            markdown_content, seal_img_list = fn_to_markdown_v2(file_path, convert_to_scanned=False, ai_seal=True, ai_model='InternVL3-38B')
        else:
            markdown_content, seal_img_list = fn_to_markdown_v2(file_path, convert_to_scanned=True, ai_seal=True, ai_model='InternVL3-38B')
        
        if not markdown_content:
            return default_result
        
        # V1.6 system prompt for broker interest analysis
        sys_prompt = """You are a senior bank custody manager responsible for extracting fund interest change elements from client-provided documents and outputting structured JSON arrays.

Field Definitions:
1. Product Name: Extract specific fund names, series fund names, or company names
2. Product Type: single_product / series_product / company_wide_product
3. Interest Rate (Annualized): Nested JSON with rate segments
   - Single rate: {"all": "X.X%"}
   - By customer type: {"individual": "0.10%", "non_individual": "0.15%"}
   - By date segments: {"START:2024-11-10": "0.15%", "2024-11-11:END": "0.10%"}
4. Start Time: YYYY-MM-DD format
5. End Time: YYYY-MM-DD format (empty if not specified)
6. Interest Days: Integer or empty string
7. Remarks: Important supplementary information

Output JSON array format:
[
  {
    "product_name": "Fund Name",
    "product_type": "single_product",
    "interest_rate": {"all": "1.4%"},
    "start_time": "2025-01-02",
    "end_time": "",
    "interest_days": 360,
    "remarks": "Monthly settlement before 20th"
  }
]
"""
        
        chat_bot = ChatBot(system_prompt=sys_prompt)
        res = chat_bot.chat(markdown_content, top_p=0.8, temperature=0.2)
        
        try:
            result = markdown_json_to_dict(res)
            if isinstance(result, list) and len(result) > 0:
                return result
            else:
                return default_result
        except Exception as e:
            print(f"Broker interest result parsing failed: {e}")
            return default_result
            
    except Exception as e:
        print(f"Broker interest analysis failed: {e}")
        return default_result


# 旧版本的 account_opening_analysis 函数已删除，请使用 account_opening_analysis_v12


def financial_product_analysis(img_path):
    """
    理财产品说明书分析（对应理财产品说明书v1.3）
    """
    try:
        # 默认返回格式
        default_result = {
            "销售机构": ["/"]
        }
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"文件不存在: {img_path}")
            return default_result
        
        # 判断文件类型
        file_ext = img_path.lower()
        image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
        is_image = any(file_ext.endswith(ext) for ext in image_formats)
        is_pdf = file_ext.endswith('.pdf')

        # 从数据库获取提示词配置
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('wealth_management', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            img_prompt = prompt_config.prompt_content
        else:
            # 如果数据库中没有配置，使用默认的理财产品说明书v1.3中的prompt
            img_prompt = """请从图片中提取理财产品说明书的销售机构信息，严格按照JSON格式输出：

请注意，输出格式必须为json格式，不要输出其他内容。

# 示例输出(仅供参考，请根据实际情况输出)
```json
{
  "销售机构": [
    "宁波银行股份有限公司",
    "交通银行股份有限公司"
  ]
}
```
"""

        if is_image:
            # 图片分析
            try:
                res = chat_bot_img(img_prompt, img_url=img_path, temperature=0.3, model='qwen2.5vl:32b')
                result = markdown_to_json(res)
                if isinstance(result, dict) and 'error' not in result:
                    return {**default_result, **result}
                else:
                    return default_result
            except Exception as e:
                print(f"理财产品图片分析失败: {e}")
                return default_result
        
        elif is_pdf:
            # PDF文件处理
            try:
                # 使用process_file_to_markdown进行PDF转换
                markdown_content = process_file_to_markdown(img_path)
                if not markdown_content:
                    return default_result

                # 相关文档检索 - 查找销售机构信息
                father_docs = markdown_content.split("\n\n")
                refer_docs = []
                keywords = ["销售", "代销", "代理销售"]
                keywords2 = ["公司", "银行"]
                for father_doc in father_docs:
                    is_match = False
                    for keyword in keywords:
                        if keyword in father_doc:
                            for keyword2 in keywords2:
                                if keyword2 in father_doc:
                                    refer_docs.append(father_doc)
                                    is_match = True
                                    break
                    if is_match:
                        break
                
                refer_doc_text = "\n".join(refer_docs) if refer_docs else markdown_content[:2000]  # 如果没找到相关文档，使用前2000字符

                # 使用ChatBot进行markdown内容分析
                chatbot = ChatBot(
                    model='qwen3-32b',
                    system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。
                    注意：请勿捏造数据，请根据实际情况输出。
                    请注意，输出格式必须为json格式，不要输出其他内容。

                    # 示例输出(仅供参考，请根据实际情况输出)
                    ```json
                    {
                      "销售机构": [
                        "XX银行股份有限公司"
                      ]
                    }
                    ```
                    """
                )
                response = chatbot.chat(refer_doc_text + "\n\n请按照理财产品说明书的标准格式输出JSON结果。")
                result = markdown_to_json(response)
                
                if isinstance(result, dict) and 'error' not in result:
                    return {**default_result, **result}
                else:
                    return default_result
                    
            except Exception as e:
                print(f"理财产品PDF分析失败: {e}")
                return default_result
        else:
            print(f"不支持的文件格式: {file_ext}")
            return default_result
            
    except Exception as e:
        print(f"理财产品说明书分析整体失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def financial_product_analysis_v15(file_path):
    """
    理财产品说明书分析V1.5版本（基于notebook逻辑）
    使用fn_to_markdown_v2、rag_search_neighbor和ChatBot进行分析
    """
    try:
        # 默认返回格式
        default_result = {
            "销售机构": ["/"]
        }
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"[DEBUG] 文件不存在: {file_path}")
            return default_result
        
        print(f"[DEBUG] 开始分析理财产品说明书V1.5: {file_path}")
        
        # 使用与notebook V1.5相同的模型
        usage_model = 'InternVL3-38B'
        
        # 使用fn_to_markdown_v2，参数与V1.5 notebook保持一致
        print(f"[DEBUG] 使用fn_to_markdown_v2转换文档...")
        markdown_content, seal_img_list = fn_to_markdown_v2(
            file_path, 
            convert_to_scanned=True, 
            ai_seal=True, 
            ai_model=usage_model
        )
        
        if not markdown_content:
            print(f"[DEBUG] fn_to_markdown_v2转换失败")
            return default_result
        
        print(f"[DEBUG] 文档转换成功，内容长度: {len(markdown_content)}")
        
        # 简化的参考文档提取（与V1.5 notebook一致）
        print(f"[DEBUG] 使用rag_search_neighbor提取相关文档...")
        refer_docs, refer_doc_text = rag_search_neighbor(
            markdown_content, 
            keywords=["销售", "代销", "代理销售"], 
            keywords2=["公司", "银行"]
        )
        
        if not refer_doc_text:
            print(f"[DEBUG] 未找到相关文档，使用完整内容前2000字符")
            refer_doc_text = markdown_content[:2000]
        else:
            print(f"[DEBUG] 找到相关文档，长度: {len(refer_doc_text)}")
        
        # 使用ChatBot进行分析，与V1.5 notebook完全一致
        print(f"[DEBUG] 使用ChatBot进行分析...")
        chatbot = ChatBot(
            model=usage_model,  # 使用统一的模型变量
            system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。
    注意：请勿捏造数据，请根据实际情况输出。
    请注意：
    - 输出格式必须为json格式，不要输出其他内容。
    - 如不存在销售机构，则输出销售机构为本银行

    # 示例输出(仅供参考，请根据实际情况输出)
    ```json
    {
      "销售机构": [
        "XX银行股份有限公司",
        "XX银行股份有限公司"
      ]
    }
    ```
    """
        )
        
        # 使用与V1.5 notebook相同的参数
        response = chatbot.chat(refer_doc_text, top_p=0.75, temperature=0.3)
        
        if not response:
            print(f"[DEBUG] ChatBot响应为空")
            return default_result
        
        print(f"[DEBUG] ChatBot响应: {response[:200]}...")
        
        # 使用标准的JSON解析方法（与V1.5 notebook一致）
        json_data = markdown_json_to_dict(response)
        
        if isinstance(json_data, dict) and 'error' not in json_data:
            print(f"[DEBUG] 解析成功: {json_data}")
            return {**default_result, **json_data}
        else:
            print(f"[DEBUG] JSON解析失败，返回默认结果")
            return default_result
            
    except Exception as e:
        print(f"[ERROR] 理财产品说明书V1.5分析失败: {e}")
        import traceback
        traceback.print_exc()
        return {"error": f"分析失败: {str(e)}"}


def financial_product_analysis_unified(file_path, use_v15_logic=True):
    """
    统一的理财产品说明书分析入口
    
    Args:
        file_path: 文件路径
        use_v15_logic: 是否使用V1.5逻辑，默认True
    
    Returns:
        dict: 分析结果
    """
    try:
        if use_v15_logic:
            print(f"[DEBUG] 使用V1.5逻辑分析理财产品说明书")
            return financial_product_analysis_v15(file_path)
        else:
            print(f"[DEBUG] 使用原有逻辑分析理财产品说明书")
            return financial_product_analysis(file_path)
    except Exception as e:
        print(f"[ERROR] 统一理财产品分析失败: {e}")
        return {"error": f"分析失败: {str(e)}"}


def financial_product_manual_analysis(img_path):
    """
    理财产品说明书分析
    """
    # 判断文件类型
    file_ext = img_path.lower()
    
    # 支持的图片格式
    image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
    is_image = any(file_ext.endswith(ext) for ext in image_formats)
    is_pdf = file_ext.endswith('.pdf')
    
    if not (is_image or is_pdf):
        raise ValueError(f"不支持的文件格式，仅支持PDF和图片格式 {image_formats}")
    
    # 如果是图片文件，先转换为PDF
    if is_image:
        # 生成临时PDF文件路径
        import tempfile
        temp_dir = os.path.dirname(img_path)
        base_name = os.path.splitext(os.path.basename(img_path))[0]
        pdf_path = os.path.join(temp_dir, f"temp_{base_name}.pdf")
        
        # 将图片转换为PDF
        image_to_pdf(img_path, pdf_path)
        
        # 删除临时PDF的标志
        should_delete_pdf = True
    else:
        # 直接使用PDF文件
        pdf_path = img_path
        should_delete_pdf = False
    
    try:
        # 生成markdown文件路径
        markdown_path = pdf_path.replace('.pdf', '.md')
        
        # PDF转markdown
        pdf_to_markdown(pdf_path, markdown_path)
        
        # 读取markdown内容
        with open(markdown_path, "r", encoding='utf-8') as f:
            markdown_content = f.read()
        
        # 删除临时markdown文件
        if os.path.exists(markdown_path):
            os.remove(markdown_path)

        father_docs = markdown_content.split("\n\n")
        # 相关文档检索
        refer_docs = []
        keywords = ["销售", "代销", "代理销售"]
        keywords2 = ["公司", "银行"]
        for father_doc in father_docs:
            is_match = False
            for keyword in keywords:
                if keyword in father_doc:
                    for keyword2 in keywords2:
                        if keyword2 in father_doc:
                            refer_docs.append(father_doc)
                            is_match = True
                            break
            if is_match:
                break
        refer_doc_text = "\n".join(refer_docs)
        
        # 使用ChatBot进行markdown内容分析
        chatbot = ChatBot(
            model='qwen3-32b',
            system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，回答用户的问题。
            请注意，输出格式必须为json格式，不要输出其他内容。

            # 示例输出(仅供参考，请根据实际情况输出)
            ```json
            {
              "sell_coms": [
                "宁波银行股份有限公司",
                "交通银行股份有限公司"
              ]
            }
            ```
            """
            )
        response = chatbot.chat(refer_doc_text + "\n/no_think")
        return markdown_to_json(response)
        
    finally:
        # 如果是从图片转换的临时PDF，需要删除
        if should_delete_pdf and os.path.exists(pdf_path):
            os.remove(pdf_path)


# ==================== 统一的期货账户分析入口 ====================

def futures_account_analysis_unified(file_path, use_v16_logic=True, enable_seal_recognition=True, enable_quality_enhancement=False):
    """
    统一的期货账户分析入口函数 - 整合V1.6逻辑和标准逻辑的优点
    
    Args:
        file_path (str): 文件路径
        use_v16_logic (bool): 是否使用V1.6版本的完整逻辑，默认True
        enable_seal_recognition (bool): 是否启用印章识别，默认True
        enable_quality_enhancement (bool): 是否启用图片质量增强，默认False
    
    Returns:
        dict: 标准化的期货账户分析结果
    """
    import logging
    
    # 设置日志
    logger = logging.getLogger(__name__)
    
    # 标准化返回格式的默认值
    default_result = {
        "产品名称": "/",
        "资金账号": "/",
        "会员号": {
            "上期所": "/",
            "大商所": "/",
            "郑商所": "/",
            "中金所": "/",
            "上能所": "/",
            "广期所": "/"
        },
        "交易编码": {
            "上期所": {"投机": "/", "套利": "/", "套保": "/"},
            "大商所": {"投机": "/", "套利": "/", "套保": "/"},
            "郑商所": {"投机": "/", "套利": "/", "套保": "/"},
            "中金所": {"投机": "/", "套利": "/", "套保": "/"},
            "上能所": {"投机": "/", "套利": "/", "套保": "/"},
            "广期所": {"投机": "/", "套利": "/", "套保": "/"}
        },
        "开始时间": "/",
        "结束时间": "/"
    }
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return {"error": f"文件不存在: {file_path}", **default_result}
        
        logger.info(f"开始分析期货账户文件: {file_path}")
        logger.info(f"使用V1.6逻辑: {use_v16_logic}, 印章识别: {enable_seal_recognition}, 质量增强: {enable_quality_enhancement}")
        
        # 获取系统提示词
        system_prompt = get_futures_account_system_prompt()
        
        # 判断文件类型
        file_ext = os.path.splitext(file_path)[1].lower()
        is_image = file_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp']
        is_pdf = file_ext == '.pdf'
        
        if use_v16_logic:
            # 使用V1.6版本的完整逻辑
            result = _analyze_with_v16_logic(file_path, system_prompt, enable_seal_recognition, enable_quality_enhancement)
        else:
            # 使用标准逻辑
            result = _analyze_with_standard_logic(file_path, system_prompt, is_image)
        
        # 验证和补充结果
        if isinstance(result, dict) and 'error' not in result:
            # 合并默认值，确保结构完整
            for key in default_result:
                if key not in result:
                    result[key] = default_result[key]
            
            logger.info(f"期货账户分析成功: {file_path}")
            return result
        else:
            logger.warning(f"期货账户分析返回异常结果: {result}")
            return default_result
            
    except Exception as e:
        logger.error(f"期货账户分析失败: {file_path}, 错误: {str(e)}")
        return {"error": f"分析失败: {str(e)}", **default_result}


def get_futures_account_system_prompt():
    """
    获取期货账户分析的系统提示词
    优先从数据库获取，如果没有则使用默认提示词
    """
    try:
        from services.database_service import DatabaseService
        db_service = DatabaseService()
        prompt_config = db_service.get_prompt_config('futures_account', 'system_prompt')
        
        if prompt_config and prompt_config.prompt_content:
            return prompt_config.prompt_content
    except Exception as e:
        print(f"从数据库获取提示词失败，使用默认提示词: {e}")
    
    # 默认提示词（与V1.6版本一致）
    return """你是一名期货开户文件解析专家。请严格区分"会员号"（固定 4 位数字）和"交易编码"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填"/"）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为"投机"）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填"/"）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填"/"）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所／上海交易所
- 大商所＝大连商品交易所／大连交易所
- 郑商所＝郑州商品交易所／郑州交易所
- 中金所＝中国金融期货交易所／金融交易所
- 上能所＝上海能源交易所／能源中心
- 广期所＝广州期货交易所／广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认"投机"。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
```json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
```
"""


def _analyze_with_v16_logic(file_path, system_prompt, enable_seal_recognition, enable_quality_enhancement):
    """
    使用V1.6版本逻辑进行分析
    """
    try:
        # 使用fn_to_markdown_v2进行文件处理
        if file_path.lower().endswith('.pdf'):
            # PDF文件处理
            markdown_content, seal_img_list = fn_to_markdown_v2(
                file_path, 
                convert_to_scanned=False, 
                ai_seal=enable_seal_recognition, 
                ai_model='InternVL3-38B', 
                add_ocr_info=False
            )
        else:
            # 图片文件处理
            markdown_content, seal_img_list = fn_to_markdown_v2(
                file_path, 
                convert_to_scanned=True, 
                ai_seal=enable_seal_recognition, 
                ai_model='InternVL3-38B'
            )
        
        # 创建ChatBot实例
        chatbot = ChatBot(model='InternVL3-38B', system_prompt=system_prompt)
        
        # 根据是否有印章图片选择处理方式
        if enable_seal_recognition and len(seal_img_list) > 0:
            response = chatbot.chat_with_img(markdown_content, img_url=seal_img_list)
        else:
            response = chatbot.chat(markdown_content)
        
        # 解析JSON结果
        result = markdown_to_json(response)
        return result
        
    except Exception as e:
        raise Exception(f"V1.6逻辑分析失败: {str(e)}")


def _analyze_with_standard_logic(file_path, system_prompt, is_image):
    """
    使用标准逻辑进行分析
    """
    try:
        if is_image:
            # 图片分析
            res = chat_bot_img(system_prompt, img_url=file_path, temperature=0.3, model='qwen2.5vl:32b')
            result = markdown_to_json(res)
            return result
        else:
            # PDF文件处理
            markdown_content = process_file_to_markdown(file_path)
            if not markdown_content:
                raise Exception("无法提取文件内容")
            
            chatbot = ChatBot(
                model='qwen3-32b',
                system_prompt="""你是一名期货开户文件解析专家。请根据用户提供的期货开户/备案文件内容，提取相关信息并按JSON格式输出。请注意，输出格式必须为json格式，不要输出其他内容。"""
            )
            
            response = chatbot.chat(markdown_content + "\n\n请按照期货账户分析的标准格式输出JSON结果。")
            result = markdown_to_json(response)
            return result
            
    except Exception as e:
        raise Exception(f"标准逻辑分析失败: {str(e)}")


# ==================== 期货账户结果验证和后处理 ====================

def validate_futures_account_result(result):
    """
    验证期货账户分析结果的格式和内容
    
    Args:
        result (dict): 分析结果
    
    Returns:
        tuple: (is_valid, error_messages)
    """
    error_messages = []
    
    if not isinstance(result, dict):
        return False, ["结果不是字典格式"]
    
    # 检查必需字段
    required_fields = ["产品名称", "资金账号", "会员号", "交易编码", "开始时间", "结束时间"]
    for field in required_fields:
        if field not in result:
            error_messages.append(f"缺少必需字段: {field}")
    
    # 检查会员号格式（4位数字或"/"）
    if "会员号" in result and isinstance(result["会员号"], dict):
        for exchange, member_no in result["会员号"].items():
            if member_no != "/" and (not isinstance(member_no, str) or len(member_no) != 4 or not member_no.isdigit()):
                error_messages.append(f"{exchange}会员号格式错误: {member_no}（应为4位数字或'/'）")
    
    # 检查交易编码格式（8位数字或"/"）
    if "交易编码" in result and isinstance(result["交易编码"], dict):
        for exchange, codes in result["交易编码"].items():
            if isinstance(codes, dict):
                for purpose, code in codes.items():
                    if code != "/" and (not isinstance(code, str) or len(code) != 8 or not code.isdigit()):
                        error_messages.append(f"{exchange}-{purpose}交易编码格式错误: {code}（应为8位数字或'/'）")
    
    return len(error_messages) == 0, error_messages


def enhance_futures_account_result(result):
    """
    增强期货账户分析结果，添加统计信息和验证状态
    
    Args:
        result (dict): 原始分析结果
    
    Returns:
        dict: 增强后的结果
    """
    enhanced_result = result.copy()
    
    # 添加验证信息
    is_valid, error_messages = validate_futures_account_result(result)
    enhanced_result["validation"] = {
        "is_valid": is_valid,
        "error_messages": error_messages
    }
    
    # 添加统计信息
    stats = {
        "total_exchanges": 6,
        "exchanges_with_member_no": 0,
        "exchanges_with_trading_codes": 0,
        "total_trading_codes": 0
    }
    
    if "会员号" in result and isinstance(result["会员号"], dict):
        stats["exchanges_with_member_no"] = sum(1 for v in result["会员号"].values() if v != "/")
    
    if "交易编码" in result and isinstance(result["交易编码"], dict):
        for exchange, codes in result["交易编码"].items():
            if isinstance(codes, dict):
                exchange_has_codes = any(code != "/" for code in codes.values())
                if exchange_has_codes:
                    stats["exchanges_with_trading_codes"] += 1
                stats["total_trading_codes"] += sum(1 for code in codes.values() if code != "/")
    
    enhanced_result["statistics"] = stats
    
    return enhanced_result


# ==================== 期货账户功能测试 ====================

def test_futures_account_analysis(test_file_path=None, use_v16_logic=True):
    """
    测试期货账户分析功能
    
    Args:
        test_file_path (str): 测试文件路径，如果为None则使用模拟数据
        use_v16_logic (bool): 是否使用V1.6逻辑
    
    Returns:
        dict: 测试结果
    """
    print("\n=== 期货账户分析功能测试 ===")
    
    if test_file_path and os.path.exists(test_file_path):
        print(f"测试文件: {test_file_path}")
        print(f"使用V1.6逻辑: {use_v16_logic}")
        
        try:
            # 执行分析
            result = futures_account_analysis_unified(
                test_file_path, 
                use_v16_logic=use_v16_logic,
                enable_seal_recognition=True,
                enable_quality_enhancement=False
            )
            
            # 验证结果
            is_valid, error_messages = validate_futures_account_result(result)
            
            # 增强结果
            enhanced_result = enhance_futures_account_result(result)
            
            print("\n--- 分析结果 ---")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            print("\n--- 验证结果 ---")
            print(f"验证通过: {is_valid}")
            if error_messages:
                print("错误信息:")
                for msg in error_messages:
                    print(f"  - {msg}")
            
            print("\n--- 统计信息 ---")
            stats = enhanced_result.get("statistics", {})
            print(f"总交易所数量: {stats.get('total_exchanges', 0)}")
            print(f"有会员号的交易所: {stats.get('exchanges_with_member_no', 0)}")
            print(f"有交易编码的交易所: {stats.get('exchanges_with_trading_codes', 0)}")
            print(f"交易编码总数: {stats.get('total_trading_codes', 0)}")
            
            return {
                "success": True,
                "result": result,
                "validation": {"is_valid": is_valid, "error_messages": error_messages},
                "statistics": stats
            }
            
        except Exception as e:
            print(f"测试失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    else:
        print("未提供测试文件，执行模拟测试...")
        
        # 模拟测试结果验证
        mock_result = {
            "产品名称": "测试期货产品",
            "资金账号": "1234567",
            "会员号": {
                "上期所": "0123",
                "大商所": "/",
                "郑商所": "0456",
                "中金所": "/",
                "上能所": "/",
                "广期所": "/"
            },
            "交易编码": {
                "上期所": {"投机": "********", "套利": "/", "套保": "/"},
                "大商所": {"投机": "/", "套利": "/", "套保": "/"},
                "郑商所": {"投机": "********", "套利": "/", "套保": "/"},
                "中金所": {"投机": "/", "套利": "/", "套保": "/"},
                "上能所": {"投机": "/", "套利": "/", "套保": "/"},
                "广期所": {"投机": "/", "套利": "/", "套保": "/"}
            },
            "开始时间": "2025-01-01",
            "结束时间": "/"
        }
        
        # 验证模拟结果
        is_valid, error_messages = validate_futures_account_result(mock_result)
        enhanced_result = enhance_futures_account_result(mock_result)
        
        print("\n--- 模拟结果验证 ---")
        print(f"验证通过: {is_valid}")
        if error_messages:
            print("错误信息:")
            for msg in error_messages:
                print(f"  - {msg}")
        
        print("\n--- 统计信息 ---")
        stats = enhanced_result.get("statistics", {})
        print(f"总交易所数量: {stats.get('total_exchanges', 0)}")
        print(f"有会员号的交易所: {stats.get('exchanges_with_member_no', 0)}")
        print(f"有交易编码的交易所: {stats.get('exchanges_with_trading_codes', 0)}")
        print(f"交易编码总数: {stats.get('total_trading_codes', 0)}")
        
        return {
            "success": True,
            "result": mock_result,
            "validation": {"is_valid": is_valid, "error_messages": error_messages},
            "statistics": stats,
            "note": "这是模拟测试结果"
        }


def test_futures_account_analysis():
    """
    测试期货账户分析功能
    """
    print("=== 测试期货账户分析功能 ===")
    
    # 模拟测试数据
    test_result = {
        "产品名称": "测试期货产品",
        "资金账号": "**********",
        "会员号": {
            "上期所": "0123",
            "大商所": "/",
            "郑商所": "0456",
            "中金所": "0170",
            "上能所": "/",
            "广期所": "0021"
        },
        "交易编码": {
            "上期所": {"投机": "********", "套利": "/", "套保": "/"},
            "大商所": {"投机": "/", "套利": "/", "套保": "/"},
            "郑商所": {"投机": "********", "套利": "/", "套保": "/"},
            "中金所": {"投机": "********", "套利": "/", "套保": "/"},
            "上能所": {"投机": "/", "套利": "/", "套保": "/"},
            "广期所": {"投机": "********", "套利": "/", "套保": "/"}
        },
        "开始时间": "2025-01-01",
        "结束时间": "/"
    }
    
    # 验证结果格式
    validated_result = validate_futures_account_result(test_result)
    print(f"验证结果: {validated_result}")
    
    # 增强结果统计
    enhanced_result = enhance_futures_account_result(test_result)
    print(f"增强结果: {enhanced_result}")
    
    print("测试完成！")
    return enhanced_result


def validate_financial_product_result(result):
    """
    验证理财产品分析结果格式
    
    Args:
        result (dict): 分析结果
    
    Returns:
        tuple: (is_valid, error_messages)
    """
    error_messages = []
    
    if not isinstance(result, dict):
        return False, ["结果不是字典格式"]
    
    # 检查必需字段
    required_fields = ["销售机构"]
    for field in required_fields:
        if field not in result:
            error_messages.append(f"缺少必需字段: {field}")
    
    # 检查销售机构格式（应为列表）
    if "销售机构" in result:
        if not isinstance(result["销售机构"], list):
            error_messages.append("销售机构字段应为列表格式")
        else:
            # 检查列表中的每个元素
            for i, institution in enumerate(result["销售机构"]):
                if not isinstance(institution, str):
                    error_messages.append(f"销售机构第{i+1}项应为字符串格式")
    
    return len(error_messages) == 0, error_messages


def enhance_financial_product_result(result):
    """
    增强理财产品分析结果，添加统计信息和验证状态
    
    Args:
        result (dict): 原始分析结果
    
    Returns:
        dict: 增强后的结果
    """
    enhanced_result = result.copy()
    
    # 验证结果
    is_valid, error_messages = validate_financial_product_result(result)
    enhanced_result["validation"] = {
        "is_valid": is_valid,
        "error_messages": error_messages
    }
    
    # 添加统计信息
    stats = {}
    
    # 统计销售机构信息
    if "销售机构" in result and isinstance(result["销售机构"], list):
        sales_institutions = result["销售机构"]
        stats["sales_institutions_count"] = len(sales_institutions)
        
        # 统计有效销售机构（非"/"的机构）
        valid_institutions = [inst for inst in sales_institutions if inst != "/"]
        stats["valid_sales_institutions"] = len(valid_institutions)
        
        # 统计空值或无效值
        invalid_institutions = [inst for inst in sales_institutions if inst == "/" or not inst.strip()]
        stats["invalid_sales_institutions"] = len(invalid_institutions)
    else:
        stats["sales_institutions_count"] = 0
        stats["valid_sales_institutions"] = 0
        stats["invalid_sales_institutions"] = 0
    
    enhanced_result["statistics"] = stats
    
    return enhanced_result


def test_financial_product_analysis(test_file_path=None):
    """
    测试理财产品分析功能
    """
    print("=== 测试理财产品分析功能 ===")
    
    if test_file_path:
        print(f"使用测试文件: {test_file_path}")
        try:
            # 测试V1.5逻辑
            result_v15 = financial_product_analysis_v15(test_file_path)
            print("\n--- V1.5逻辑分析结果 ---")
            print(f"销售机构: {result_v15.get('销售机构', [])}")
            
            # 验证结果格式
            is_valid, error_messages = validate_financial_product_result(result_v15)
            enhanced_result = enhance_financial_product_result(result_v15)
            
            print(f"\n验证通过: {is_valid}")
            if error_messages:
                print("错误信息:")
                for msg in error_messages:
                    print(f"  - {msg}")
            
            # 显示统计信息
            stats = enhanced_result.get("statistics", {})
            print("\n--- 统计信息 ---")
            print(f"销售机构数量: {stats.get('sales_institutions_count', 0)}")
            print(f"有效销售机构: {stats.get('valid_sales_institutions', 0)}")
            
            return {
                "success": True,
                "result": result_v15,
                "validation": {"is_valid": is_valid, "error_messages": error_messages},
                "statistics": stats
            }
            
        except Exception as e:
            print(f"测试失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    else:
        print("未提供测试文件，执行模拟测试...")
        
        # 模拟测试结果验证
        mock_result = {
            "销售机构": ["中国银行股份有限公司", "招商银行股份有限公司"]
        }
        
        # 验证模拟结果
        is_valid, error_messages = validate_financial_product_result(mock_result)
        enhanced_result = enhance_financial_product_result(mock_result)
        
        print("\n--- 模拟结果验证 ---")
        print(f"验证通过: {is_valid}")
        if error_messages:
            print("错误信息:")
            for msg in error_messages:
                print(f"  - {msg}")
        
        print("\n--- 统计信息 ---")
        stats = enhanced_result.get("statistics", {})
        print(f"销售机构数量: {stats.get('sales_institutions_count', 0)}")
        print(f"有效销售机构: {stats.get('valid_sales_institutions', 0)}")
        
        return {
            "success": True,
            "result": mock_result,
            "validation": {"is_valid": is_valid, "error_messages": error_messages},
            "statistics": stats,
            "note": "这是模拟测试结果"
        }


def compare_futures_analysis_methods(test_file_path):
    """
    比较不同期货账户分析方法的结果
    
    Args:
        test_file_path (str): 测试文件路径
    
    Returns:
        dict: 比较结果
    """
    if not os.path.exists(test_file_path):
        return {"error": f"测试文件不存在: {test_file_path}"}
    
    print("\n=== 期货账户分析方法对比测试 ===")
    print(f"测试文件: {test_file_path}")
    
    results = {}
    
    try:
        # 测试V1.6逻辑
        print("\n--- 测试V1.6逻辑 ---")
        result_v16 = futures_account_analysis_unified(
            test_file_path, 
            use_v16_logic=True,
            enable_seal_recognition=True
        )
        results["v16_logic"] = result_v16
        print("V1.6逻辑测试完成")
        
        # 测试标准逻辑
        print("\n--- 测试标准逻辑 ---")
        result_standard = futures_account_analysis_unified(
            test_file_path, 
            use_v16_logic=False,
            enable_seal_recognition=False
        )
        results["standard_logic"] = result_standard
        print("标准逻辑测试完成")
        
        # 测试原有的V1.6函数
        print("\n--- 测试原有V1.6函数 ---")
        result_original_v16 = future_account_analysis_v16(test_file_path)
        results["original_v16"] = result_original_v16
        print("原有V1.6函数测试完成")
        
        # 测试原有的标准函数
        print("\n--- 测试原有标准函数 ---")
        result_original_standard = future_account_analysis(test_file_path)
        results["original_standard"] = result_original_standard
        print("原有标准函数测试完成")
        
        # 比较结果
        print("\n--- 结果比较 ---")
        for method, result in results.items():
            is_valid, error_messages = validate_futures_account_result(result)
            print(f"{method}: 验证通过={is_valid}, 错误数量={len(error_messages)}")
        
        return {"success": True, "results": results}
        
    except Exception as e:
        print(f"比较测试失败: {str(e)}")
        return {"success": False, "error": str(e), "partial_results": results}


# ===== 新增功能模块升级版本 =====

def account_opening_analysis_v12_multi(file_paths):
    """
    账户开户场景多文件合并分析 V1.2版本
    基于notebook逻辑：账户开户场景V1.2.ipynb
    支持多文档处理和印章完整性检测

    Args:
        file_paths: 文件路径列表，支持多个文件合并分析

    Returns:
        dict: 合并后的分析结果
    """
    try:
        default_result = {
            "manager_info": {"name": "/", "address": "/", "contact": "/"},
            "investor_info": {"name": "/", "type": "/", "account_nature": "/"},
            "contact_info": [],
            "seal_integrity": "unknown",
            "page_continuity": "unknown",
            "file_count": len(file_paths) if isinstance(file_paths, list) else 1,
            "processed_files": []
        }

        # 如果传入的是单个文件路径字符串，转换为列表
        if isinstance(file_paths, str):
            file_paths = [file_paths]

        if not file_paths:
            return default_result

        # 验证所有文件是否存在
        valid_files = []
        for file_path in file_paths:
            if os.path.exists(file_path):
                valid_files.append(file_path)
            else:
                print(f"文件不存在，跳过: {file_path}")

        if not valid_files:
            return default_result

        # 合并所有文档的OCR结果
        combined_md = ""
        all_seal_img_lists = []
        all_page_data = []

        for file_path in valid_files:
            try:
                # 1. 文档OCR处理
                ht_md, seal_img_list = fn_to_markdown_v2(file_path)

                if ht_md:
                    combined_md += f"\n\n=== 文件: {os.path.basename(file_path)} ===\n"
                    combined_md += ht_md
                    default_result["processed_files"].append(os.path.basename(file_path))

                if seal_img_list:
                    all_seal_img_lists.extend(seal_img_list)

                # 收集页面数据用于连续性检测
                try:
                    page_id_data = get_page_id_by_content(file_path)['results']
                    if page_id_data:
                        all_page_data.extend([x for x in page_id_data if x is not None])
                except:
                    pass

            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {e}")
                continue

        if not combined_md:
            return default_result

        # 2. 印章完整性检测（基于所有文件的印章）
        seal_integrity = "unknown"
        if all_seal_img_lists:
            try:
                # 简单统计：如果有印章就认为完整
                seal_integrity = "complete" if len(all_seal_img_lists) > 0 else "incomplete"
            except:
                pass

        # 3. 页面连续性检测（基于所有文件的页面）
        page_continuity = "unknown"
        if all_page_data:
            try:
                import numpy as np
                if len(all_page_data) <= 1:
                    page_continuity = "continuous"
                else:
                    sorted_arr = np.sort(all_page_data)
                    page_continuity = "continuous" if (np.all(np.diff(sorted_arr) == 1) == np.True_) else "discontinuous"
            except:
                pass

        # 4. 初始化ChatBot - 基于notebook的system_prompt
        chatbot = ChatBot(
            model='InternVL3-38B',
            system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的部分内容，以json形式回答用户的问题
请注意：
- 输出格式必须为json格式，不要输出其他内容。
- 请勿捏造数据，请根据实际情况输出。
- 用户导入的内容为OCR识别后的内容，可能存在错误，请修复后输出

# 示例输出(仅供参考，请根据实际情况输出)
```json
{
  "账户名称": "XX股权投资合伙企业（有限合伙）"
}
```"""
        )

        # 5. 使用RAG搜索相关信息 - 基于合并后的文档内容
        try:
            # 搜索账户名称相关信息
            _, zhmc_ref_text = rag_search_neighbor(combined_md, keywords=["名称"], neighbor_count=2)

            # 搜索联系人信息
            _, lxfs_ref_text = rag_search_neighbor(combined_md, keywords=['联系人', '电话'])

        except:
            # 如果RAG搜索失败，使用合并文档的前4000字符
            zhmc_ref_text = combined_md[:4000]
            lxfs_ref_text = combined_md[:4000]

        # 6. 分步提取信息 - 基于notebook的提取逻辑

        # 6.1 提取账户名称
        account_name = "/"
        try:
            response = chatbot.chat(f"请从下面内容中提取出账户名称，并输出json格式，key为`账户名称`：\n{zhmc_ref_text}")
            import json
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                result = json.loads(json_str)
                account_name = result.get('账户名称', '/')
        except:
            pass

        # 6.2 提取账户性质
        account_nature = "/"
        try:
            response = chatbot.chat(f"""请从下面内容中提取出账户性质，并输出json格式，key为`账户性质`
注意：
- 账户性质是指银行账户的性质，如合伙企业银行托管账户、私募基金银行托管账户、理财产品托管专户二级户等

{zhmc_ref_text}""")
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                result = json.loads(json_str)
                account_nature = result.get('账户性质', '/')
        except:
            pass

        # 6.3 提取联系人信息
        contact_info = []
        try:
            response = chatbot.chat(f"""请从下面内容中提取出资产管理人的联系人及联系电话，输出json格式，key为id，value为一个List，List中包含联系人及联系电话

{lxfs_ref_text}""")
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                result = json.loads(json_str)
                contact_list = result.get('id', [])
                for contact in contact_list:
                    if isinstance(contact, dict):
                        contact_info.append({
                            "contact_person": contact.get('联系人', ''),
                            "phone": contact.get('联系电话', '')
                        })
        except:
            pass

        # 7. 构建最终结果
        final_result = {
            "manager_info": {
                "name": account_name,  # 使用账户名称作为管理机构名称
                "address": "/",
                "contact": "/"
            },
            "investor_info": {
                "name": account_name,
                "type": "/",
                "account_nature": account_nature
            },
            "contact_info": contact_info,
            "seal_integrity": seal_integrity,
            "page_continuity": page_continuity,
            "file_count": len(valid_files),
            "processed_files": default_result["processed_files"]
        }

        return final_result

    except Exception as e:
        print(f"账户开户多文件分析错误: {e}")
        return default_result


def account_opening_analysis_v12(file_path):
    """
    账户开户场景分析 V1.2版本 - 单文件版本
    基于notebook逻辑：账户开户场景V1.2.ipynb
    支持多文档处理和印章完整性检测

    注意：这是单文件版本，推荐使用 account_opening_analysis_v12_multi 进行多文件分析
    """
    try:
        default_result = {
            "manager_info": {"name": "/", "address": "/", "contact": "/"},
            "investor_info": {"name": "/", "type": "/", "account_nature": "/"},
            "contact_info": [],
            "seal_integrity": "unknown",
            "page_continuity": "unknown"
        }

        if not os.path.exists(file_path):
            return default_result

        # 1. 文档OCR处理
        ht_md, seal_img_list = fn_to_markdown_v2(file_path)

        if not ht_md:
            return default_result

        # 2. 印章完整性检测
        seal_integrity = "unknown"
        if seal_img_list:
            try:
                seal_info = get_seal_info(file_path)
                if seal_info and 'results' in seal_info:
                    seal_results = seal_info['results']
                    if seal_results:
                        seal_integrity = "complete" if len(seal_results) > 0 else "incomplete"
            except:
                pass

        # 3. 页面连续性检测
        page_continuity = "unknown"
        try:
            page_id_data = get_page_id_by_content(file_path)['results']
            page_id_data_pass_None = [x for x in page_id_data if x is not None]
            if page_id_data_pass_None:
                # 使用numpy检查连续性
                import numpy as np
                if len(page_id_data_pass_None) <= 1:
                    page_continuity = "continuous"
                else:
                    sorted_arr = np.sort(page_id_data_pass_None)
                    page_continuity = "continuous" if (np.all(np.diff(sorted_arr) == 1) == np.True_) else "discontinuous"
        except:
            pass

        # 4. 初始化ChatBot - 基于notebook的system_prompt
        chatbot = ChatBot(
            model='InternVL3-38B',
            system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的部分内容，以json形式回答用户的问题
请注意：
- 输出格式必须为json格式，不要输出其他内容。
- 请勿捏造数据，请根据实际情况输出。
- 用户导入的内容为OCR识别后的内容，可能存在错误，请修复后输出

# 示例输出(仅供参考，请根据实际情况输出)
```json
{
  "账户名称": "XX股权投资合伙企业（有限合伙）"
}
```"""
        )

        # 5. 使用RAG搜索相关信息 - 基于notebook逻辑
        try:
            # 搜索账户名称相关信息
            _, zhmc_ref_text = rag_search_neighbor(ht_md, keywords=["名称"], neighbor_count=2)

            # 搜索联系人信息
            _, lxfs_ref_text = rag_search_neighbor(ht_md, keywords=['联系人', '电话'])

        except:
            # 如果RAG搜索失败，使用全文
            zhmc_ref_text = ht_md[:4000]
            lxfs_ref_text = ht_md[:4000]

        # 6. 分步提取信息 - 基于notebook的提取逻辑

        # 6.1 提取账户名称
        account_name = "/"
        try:
            response = chatbot.chat(f"请从下面内容中提取出账户名称，并输出json格式，key为`账户名称`：\n{zhmc_ref_text}")
            import json
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                result = json.loads(json_str)
                account_name = result.get('账户名称', '/')
        except:
            pass

        # 6.2 提取账户性质
        account_nature = "/"
        try:
            response = chatbot.chat(f"""请从下面内容中提取出账户性质，并输出json格式，key为`账户性质`
注意：
- 账户性质是指银行账户的性质，如合伙企业银行托管账户、私募基金银行托管账户、理财产品托管专户二级户等

{zhmc_ref_text}""")
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                result = json.loads(json_str)
                account_nature = result.get('账户性质', '/')
        except:
            pass

        # 6.3 提取联系人信息
        contact_info = []
        try:
            response = chatbot.chat(f"""请从下面内容中提取出资产管理人的联系人及联系电话，输出json格式，key为id，value为一个List，List中包含联系人及联系电话

{lxfs_ref_text}""")
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                result = json.loads(json_str)
                contact_list = result.get('id', [])
                for contact in contact_list:
                    if isinstance(contact, dict):
                        contact_info.append({
                            "contact_person": contact.get('联系人', ''),
                            "phone": contact.get('联系电话', '')
                        })
        except:
            pass

        # 7. 构建最终结果
        final_result = {
            "manager_info": {
                "name": account_name,  # 使用账户名称作为管理机构名称
                "address": "/",
                "contact": "/"
            },
            "investor_info": {
                "name": account_name,
                "type": "/",
                "account_nature": account_nature
            },
            "contact_info": contact_info,
            "seal_integrity": seal_integrity,
            "page_continuity": page_continuity
        }

        return final_result

    except Exception as e:
        print(f"账户开户分析错误: {e}")
        return default_result


def non_standard_trade_analysis_v13(file_path):
    """
    Non-standard Trade Analysis V1.3 - Based on notebook logic
    Supports multiple document formats with precision protection
    """
    try:
        default_result = [{
            "investor_name": "/",
            "investor_account": "/",
            "business_date": "/",
            "business_type": "/",
            "investment_target_name": "/",
            "investment_target_code": "/",
            "investment_amount": "/",
            "investment_quantity": "/",
            "transaction_fee": "/"
        }]
        
        if not os.path.exists(file_path):
            return default_result
        
        # Handle different file formats
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext in ['.xlsx', '.xls']:
            # Excel file processing with precision protection
            try:
                import pandas as pd
                # Read as string to preserve precision
                df = pd.read_excel(file_path, dtype=str, engine='openpyxl')
                
                # Convert to markdown table format
                markdown_content = df.to_markdown(index=False)
                
            except Exception as e:
                print(f"Excel processing failed: {e}")
                return default_result
                
        elif file_ext == '.pdf':
            # PDF processing
            markdown_content, seal_img_list = fn_to_markdown_v2(file_path, convert_to_scanned=False, ai_seal=True, ai_model='InternVL3-38B')
            
        else:
            # Image processing
            markdown_content, seal_img_list = fn_to_markdown_v2(file_path, convert_to_scanned=True, ai_seal=True, ai_model='InternVL3-38B')
        
        if not markdown_content:
            return default_result
        
        # V1.3 system prompt for non-standard trade analysis
        chatbot = ChatBot(
            model='qwen3-32b',
            system_prompt="""You are a professional non-standard transaction confirmation document analyst. Please extract transaction information from the provided document content and output in JSON format.

Extraction Fields:
1. Investor Name: Usually refers to customer name, generally the name of asset management plan
2. Investor Account: Usually refers to customer's fund account
3. Business Date: Date corresponding to a transaction
4. Business Type: Dividend, dividend reinvestment, buy, sell, subscription, purchase, redemption
5. Investment Target Name: Each transaction has an investment target, generally funds, asset management plans, etc.
6. Investment Target Code: Code of investment target, mostly combination of numbers and letters, may be empty
7. Investment Amount: Actual confirmed transaction amount (numeric format, maintain original precision, fill "/" if missing)
8. Investment Quantity: Document may describe in shares (numeric format, maintain original precision, fill "/" if missing)
9. Transaction Fee: Generally subscription, redemption, buy, sell transactions will indicate transaction fees (numeric format, maintain original precision, fill "/" if missing)

Output JSON Format:
If document contains multiple transactions, return array format. Ensure numeric fields maintain original precision without rounding. Single transaction example:

{
  "investor_name": "某某资产管理计划",
  "investor_account": "********9",
  "business_date": "2024-01-15",
  "business_type": "申购",
  "investment_target_name": "某某货币基金",
  "investment_target_code": "000001",
  "investment_amount": "1000000.00",
  "investment_quantity": "1000000.00",
  "transaction_fee": "0.00"
}
"""
        )
        
        # Analyze the document
        response = chatbot.chat(markdown_content)
        
        try:
            result = markdown_json_to_dict(response)
            
            # Ensure result is in list format
            if isinstance(result, dict):
                result = [result]
            elif not isinstance(result, list):
                return default_result
            
            # Validate and clean results
            cleaned_results = []
            for item in result:
                if isinstance(item, dict):
                    # Ensure all required fields exist
                    cleaned_item = default_result[0].copy()
                    cleaned_item.update(item)
                    cleaned_results.append(cleaned_item)
            
            return cleaned_results if cleaned_results else default_result
            
        except Exception as e:
            print(f"Non-standard trade result parsing failed: {e}")
            return default_result
            
    except Exception as e:
        print(f"Non-standard trade analysis failed: {e}")
        return default_result


def ningyin_fee_analysis_v11(file_path):
    """
    Ningyin Fee Analysis V1.1 - Based on notebook logic
    Supports PDF to Word conversion with table merge handling
    """
    try:
        default_result = {
            "product_info": [],
            "fee_changes": [],
            "effective_date": "/",
            "announcement_date": "/",
            "issuer": "宁银理财有限责任公司"
        }
        
        if not os.path.exists(file_path):
            return default_result
        
        # Check if it's a scanned PDF
        def is_scanned_pdf_simple(pdf_path, threshold=0.9):
            """Simple scanned PDF detection"""
            try:
                import PyPDF2
                with open(pdf_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    text_pages = 0
                    total_pages = len(reader.pages)
                    
                    for page in reader.pages:
                        text = page.extract_text().strip()
                        if len(text) > 50:  # Has meaningful text
                            text_pages += 1
                    
                    text_ratio = text_pages / total_pages if total_pages > 0 else 0
                    return text_ratio <= threshold
            except Exception:
                return True  # Assume scanned if can't determine
        
        # PDF to markdown conversion with table handling
        def pdf_to_markdown_ningyin(pdf_fn):
            """Convert PDF to markdown with table processing"""
            import uuid
            import docx
            from docx.table import Table
            from docx.oxml.ns import qn
            import pandas as pd
            
            try:
                # Use pdf2docx if available, otherwise fallback
                temp_fn = f"{uuid.uuid1().hex}.docx"
                
                try:
                    from pdf2docx import Converter
                    cv = Converter(pdf_fn)
                    cv.convert(temp_fn, start=0, end=None)
                    cv.close()
                except ImportError:
                    # Fallback to fn_to_markdown_v2 if pdf2docx not available
                    markdown_content, _ = fn_to_markdown_v2(pdf_fn)
                    return markdown_content
                
                # Load Word document
                doc = docx.Document(temp_fn)
                out_markdown = ""
                
                now_table = None
                header = []
                table_id = 0
                
                def check_vertical_merge(cell):
                    """Check if cell is vertically merged"""
                    tc = cell._tc
                    tc_pr = tc.get_or_add_tcPr()
                    vmerge = tc_pr.find(qn("w:vMerge"))
                    
                    if vmerge is not None:
                        val = vmerge.get(qn("w:val"))
                        if val == "restart":
                            return "restart"
                        elif val == "continue":
                            return "continue"
                    return "None"
                
                def fix_merge_table(fix_table):
                    """Fix merged table data"""
                    if fix_table is None or fix_table.empty:
                        return pd.DataFrame()
                    
                    new_table = pd.DataFrame()
                    min_length = {}
                    
                    for table_i, table in fix_table.groupby('table_id'):
                        if table_i == 0:
                            new_table = table.copy()
                            # Calculate field length for validation
                            for col in table.columns:
                                if col != 'table_id':
                                    min_length[col] = table[col].iloc[:-1].astype(str).map(len).mean()
                        else:
                            first_row = table.iloc[0]
                            # Calculate field completion rate
                            success_num = 0
                            for col in table.columns:
                                if col != 'table_id' and col in min_length:
                                    if len(str(first_row[col])) >= min_length[col]:
                                        success_num += 1
                            
                            success_ratio = success_num / len(min_length) if min_length else 0
                            
                            # Merge if completion rate is low
                            if success_ratio <= 0.7:
                                for col in table.columns:
                                    if col == 'table_id':
                                        continue
                                    fix_idx = new_table[new_table[col] != '!restart!'].iloc[-1].name
                                    new_table.loc[fix_idx, col] += str(first_row[col]).strip()
                                table = table.iloc[1:]
                            new_table = pd.concat([new_table, table], ignore_index=True)
                    
                    # Fill merged cells
                    fin_table = new_table.replace('!restart!', pd.NA).fillna(method='pad')
                    if 'table_id' in fin_table.columns:
                        del fin_table['table_id']
                    return fin_table
                
                def df_to_json_rows(df):
                    """Convert DataFrame to JSON"""
                    if df is None or df.empty:
                        return '[]'
                    json_list = df.to_dict(orient='records')
                    return json.dumps(json_list, indent=2, ensure_ascii=False)
                
                # Process document elements
                for child in doc.element.body:
                    if hasattr(child, 'text') and hasattr(child, 'tag'):
                        if 'p}p' in child.tag:  # Paragraph
                            text_content = child.text.replace('\n', '').strip() if child.text else ''
                            if text_content == '':
                                continue
                            
                            if now_table is not None:
                                # Output table as JSON when encountering text
                                out_markdown += "\n```json\n" + df_to_json_rows(fix_merge_table(now_table)) + "\n```\n"
                                now_table = None
                                header = []
                                table_id = 0
                            
                            out_markdown += text_content + '\n'
                        
                        elif 'tbl}tbl' in child.tag:  # Table
                            table = Table(child, doc)
                            data = []
                            last_tc = {}
                            
                            for row in table.rows:
                                row_data = []
                                cols_id = 0
                                for cell in row.cells:
                                    if check_vertical_merge(cell) == "restart":
                                        if last_tc.get(cols_id, None) is None:
                                            last_tc[cols_id] = cell._tc
                                        else:
                                            row_data.append('!restart!')
                                            continue
                                    
                                    text = cell.text.strip().replace('\n', '').replace('\t', ' ')
                                    row_data.append(text)
                                    cols_id += 1
                                data.append(row_data)
                            
                            if data:
                                if now_table is None:
                                    header = data[0] if data else []
                                    data = data[1:] if len(data) > 1 else []
                                
                                if header:
                                    out_df = pd.DataFrame(data, columns=header)
                                    out_df['table_id'] = table_id
                                    
                                    if now_table is None:
                                        now_table = out_df.copy()
                                    else:
                                        now_table = pd.concat([now_table, out_df])
                                    table_id += 1
                
                # Clean up
                if os.path.exists(temp_fn):
                    os.remove(temp_fn)
                
                return out_markdown
                
            except Exception as e:
                print(f"PDF to markdown conversion failed: {e}")
                # Fallback to standard conversion
                try:
                    markdown_content, _ = fn_to_markdown_v2(pdf_fn)
                    return markdown_content
                except Exception:
                    return ""
        
        # Check if PDF is scanned
        if is_scanned_pdf_simple(file_path):
            # Use OCR-based processing
            markdown_content, _ = fn_to_markdown_v2(file_path, convert_to_scanned=True, ai_seal=True)
        else:
            # Use PDF to Word conversion
            markdown_content = pdf_to_markdown_ningyin(file_path)

        # If PDF to Word conversion failed or returned empty content, try standard conversion
        if not markdown_content:
            try:
                print(f"PDF to Word conversion returned empty content, trying standard conversion...")
                markdown_content = process_file_to_markdown(file_path)
            except Exception as e:
                print(f"Standard conversion also failed: {e}")

        if not markdown_content:
            return default_result
        
        # Extract fee information using AI
        chatbot = ChatBot(
            model='InternVL3-38B',
            system_prompt="""You are a financial document analyst specializing in fee change announcements. Please extract fee change information from the provided content and return in JSON format.

Extraction Fields:
1. Product Info: Product names and codes mentioned in the document
2. Fee Changes: Specific fee changes including original rates, new rates, and effective periods
3. Effective Date: When the fee changes take effect
4. Announcement Date: Date of the announcement
5. Issuer: The issuing organization

Output JSON Format:
{
  "product_info": [
    {"product_name": "产品名称", "product_code": "产品代码"}
  ],
  "fee_changes": [
    {
      "fee_type": "费用类型",
      "original_rate": "原费率",
      "new_rate": "新费率",
      "effective_start": "生效开始日期",
      "effective_end": "生效结束日期",
      "notes": "备注信息"
    }
  ],
  "effective_date": "生效日期",
  "announcement_date": "公告日期",
  "issuer": "发行机构"
}

Note: Extract based on actual document content, do not fabricate information. If information is not available, use "/" as placeholder.
"""
        )
        
        # Analyze the document
        response = chatbot.chat(f"""Please analyze the following financial fee change announcement and extract the required information:

{markdown_content[:3000]}
""")
        
        try:
            result = markdown_json_to_dict(response)
            
            if isinstance(result, dict):
                # Merge with default result
                final_result = default_result.copy()
                final_result.update(result)
                return final_result
            else:
                return default_result
                
        except Exception as e:
            print(f"Ningyin fee result parsing failed: {e}")
            return default_result
            
    except Exception as e:
        print(f"Ningyin fee analysis failed: {e}")
        return default_result