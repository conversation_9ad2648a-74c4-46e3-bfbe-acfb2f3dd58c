# AI和机器学习相关
accelerate==1.8.1
transformers==4.53.0
torch==2.7.1
torchvision==0.22.1
huggingface-hub==0.33.2
tokenizers==0.21.2
safetensors==0.5.3
openai==1.93.0
modelscope==1.27.1

# 计算机视觉和图像处理
opencv-python==*********
opencv-python-headless==*********
scikit-image>=0.21.0
ultralytics==8.3.159
ultralytics-thop==2.0.14
albucore==0.0.24
albumentations==2.0.8
doclayout_yolo==0.0.4
thop==0.1.1.post2209072238

# 科学计算和数据处理
numpy>=1.24.0
pandas>=2.0.0
scipy==1.16.0
scikit-learn==1.7.0
matplotlib==3.10.3
matplotlib-inline==0.1.7
seaborn==0.13.2
joblib==1.5.1
threadpoolctl==3.6.0

# PDF和文档处理
PyMuPDF==1.24.9
PyMuPDFb==1.24.9
PyPDF2==3.0.1
pypdf==5.7.0
pypdfium2==4.30.0
pdf2docx==0.5.8
pdfminer.six==20250506
pdftext==0.6.3
magic-pdf==1.3.12
mineru==2.0.6

# Office文档处理
openpyxl==3.1.5
xlrd==2.0.2
xlsxwriter==3.2.5
python-pptx==1.0.2
python-docx>=0.8.11

# 图像处理
Pillow>=10.0.0
fonttools==4.58.4

# Web框架和API
Flask>=3.0.0
Flask-SQLAlchemy>=3.1.0
Flask-Login>=0.6.0
Flask-Cors==4.0.0
Werkzeug>=3.0.0
flasgger==*******
Jinja2==3.1.6
MarkupSafe==3.0.2
itsdangerous==2.2.0
blinker==1.9.0
click==8.2.1

# 数据库和ORM
SQLAlchemy>=2.0.0
greenlet==3.2.3

# 网络请求和HTTP
requests>=2.31.0
httpx==0.28.1
httpcore==1.0.9
h11==0.16.0
urllib3==2.5.0
certifi==2025.6.15
charset-normalizer==3.4.2
idna==3.10
sniffio==1.3.1

# 云服务和认证
azure-ai-documentintelligence==1.0.2
azure-core==1.35.0
azure-identity==1.23.0
boto3==1.39.3
botocore==1.39.3
s3transfer==0.13.0
jmespath==1.0.1
msal==1.32.3
msal-extensions==1.3.1

# 安全和加密
cryptography>=41.0.0
bcrypt==4.3.0
PyJWT==2.10.1
cffi==1.17.1
pycparser==2.22

# 数据验证和序列化
pydantic==2.10.6
pydantic-settings==2.10.1
pydantic_core==2.27.2
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
referencing==0.36.2
rpds-py==0.26.0

# 文本处理和NLP
beautifulsoup4==4.13.4
soupsieve==2.7
lxml==6.0.0
markdownify==1.1.0
markitdown==0.1.2
mammoth==1.9.1
fast-langdetect==0.2.5
fasttext-predict==*******
ftfy==6.3.1
regex==2024.11.6

# 音频处理
pydub==0.25.1
SpeechRecognition==3.14.3

# 系统和工具
psutil==7.0.0
platformdirs==4.3.7
filelock==3.18.0
packaging==25.0
tqdm==4.67.1
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.9.0
humanfriendly==10.0
loguru==0.7.3
termcolor==3.1.0
fire==0.7.0

# 开发和调试工具
ipython>=8.0.0
ipykernel==6.29.5
jupyter_client==8.6.3
jupyter_core==5.8.1
debugpy==1.8.11
jedi==0.19.2
parso==0.8.4
prompt-toolkit==3.0.43
Pygments==2.19.2
wcwidth==0.2.13
traitlets==5.14.3
pyzmq==27.0.0
tornado==6.5.1
nest-asyncio==1.6.0
comm==0.2.3
asttokens==3.0.0
executing==2.2.0
pure-eval==0.2.2
stack-data==0.2.0
decorator==5.2.1
matplotlib-inline==0.1.7
ipython_pygments_lexers==1.1.1

# 数据格式和压缩
PyYAML==6.0.2
Brotli==1.1.0
defusedxml==0.7.1
et_xmlfile==2.0.0
olefile==0.47
mistune==3.1.3

# 数学和几何
sympy==1.14.0
mpmath==1.3.0
shapely==2.1.1
networkx==3.5
contourpy==1.3.2
cycler==0.12.1
kiwisolver==1.4.8
pyparsing==3.2.3

# 机器学习相关工具
omegaconf==2.3.0
onnxruntime==1.22.0
protobuf==6.31.1
flatbuffers==25.2.10
dill==0.4.0

# 其他工具和库
attrs==25.3.0
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
isodate==0.7.2
typing_extensions==4.14.1
typing-inspection==0.4.1
six==1.17.0
pytz==2025.2
tzdata==2025.2
fsspec==2025.5.1
json_repair==0.47.6
jiter==0.10.0
cobble==0.1.4
distro==1.9.0
rapid-table==1.0.5
robust-downloader==0.0.2
stringzilla==3.12.5
simsimd==6.4.9
pyclipper==1.3.0.post6
py-cpuinfo==9.0.0
reportlab==4.4.2
youtube-transcript-api==1.0.3
magika==0.6.2

# Windows特定
pywin32==311
pyreadline3==3.5.4
win32_setctime==1.2.0